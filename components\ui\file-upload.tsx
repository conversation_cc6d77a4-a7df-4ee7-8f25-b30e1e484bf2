'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, FileText, AlertCircle } from 'lucide-react';
import { Button } from './button';
import { Progress } from './progress';
import { withErrorBoundary } from '@/components/error-boundary';

interface FileUploadProps {
  onUpload: (files: File[]) => Promise<void>;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedFileTypes?: string[];
}

interface UploadingFile {
  file: File;
  progress: number;
  error?: string;
}

function FileUploadComponent({
  onUpload,
  maxFiles = 5,
  maxSize = 50 * 1024 * 1024, // 50MB default
  acceptedFileTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip'],
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const newFiles = acceptedFiles.map((file) => ({
        file,
        progress: 0,
      }));

      setUploadingFiles((prev) => [...prev, ...newFiles]);

      try {
        await onUpload(acceptedFiles);
        // Update progress to 100% for successfully uploaded files
        setUploadingFiles((prev) =>
          prev.map((f) =>
            acceptedFiles.includes(f.file) ? { ...f, progress: 100 } : f
          )
        );
      } catch (error) {
        // Mark failed uploads with error message
        setUploadingFiles((prev) =>
          prev.map((f) =>
            acceptedFiles.includes(f.file)
              ? { ...f, error: 'Upload failed. Please try again.' }
              : f
          )
        );
      }
    },
    [onUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles,
    maxSize,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as { [key: string]: string[] }),
  });

  const removeFile = (fileToRemove: File) => {
    setUploadingFiles((prev) =>
      prev.filter((f) => f.file !== fileToRemove)
    );
  };

  return (
    <div className="w-full space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${
            isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-gray-200 hover:border-primary/50'
          }`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-8 w-8 text-gray-400" />
          <div className="text-sm">
            <Button
              type="button"
              variant="link"
              className="text-primary"
              tabIndex={0}
              aria-label="Click to select files or drag and drop"
            >
              Click to select files
            </Button>{' '}
            or drag and drop
          </div>
          <p className="text-xs text-gray-500">
            Maximum file size: {maxSize / 1024 / 1024}MB
            <br />
            Accepted file types: {acceptedFileTypes.join(', ')}
          </p>
        </div>
      </div>

      {uploadingFiles.length > 0 && (
        <div className="space-y-2">
          {uploadingFiles.map(({ file, progress, error }) => (
            <div
              key={`${file.name}-${file.size}`}
              className="flex items-center space-x-4 rounded-lg border p-3"
            >
              <FileText className="h-5 w-5 flex-shrink-0 text-gray-400" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{file.name}</p>
                <p className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
                {error ? (
                  <div className="flex items-center space-x-1 text-destructive text-xs mt-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                ) : (
                  <Progress value={progress} className="h-1 mt-2" />
                )}
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="flex-shrink-0"
                onClick={() => removeFile(file)}
                aria-label={`Remove ${file.name}`}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Export the component wrapped with an error boundary
export const FileUpload = withErrorBoundary(FileUploadComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in FileUpload component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});
