"use client";

import { DocumentVersion, DocumentComment } from '@/types';
import initSqlJs, { Database } from 'sql.js';
import { Document, DocumentEdit, SQLiteDatabase } from '../types/sqlitedb';

// Import schema SQL as a string
const schemaSQL = `
-- SQLite schema for document management system
PRAGMA foreign_keys = ON;

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id TEXT PRIMARY KEY,
    bidId TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    createdBy TEXT NOT NULL,
    status TEXT NOT NULL,
    lastVersionId TEXT,
    lastVersion TEXT,
    metadata TEXT
);

-- Create indexes for documents table
CREATE INDEX IF NOT EXISTS idx_documents_bidId ON documents(bidId);
CREATE INDEX IF NOT EXISTS idx_documents_updatedAt ON documents(updatedAt);

-- Document versions table
CREATE TABLE IF NOT EXISTS document_versions (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    content TEXT NOT NULL,
    version TEXT NOT NULL,
    previousVersionId TEXT,
    diff TEXT,
    createdAt TEXT NOT NULL,
    createdBy_id TEXT NOT NULL,
    createdBy_name TEXT NOT NULL,
    createdBy_role TEXT NOT NULL,
    commitMessage TEXT,
    status TEXT NOT NULL,
    FOREIGN KEY (documentId) REFERENCES documents(id)
);

-- Create indexes for document_versions table
CREATE INDEX IF NOT EXISTS idx_versions_documentId ON document_versions(documentId);
CREATE INDEX IF NOT EXISTS idx_versions_createdAt ON document_versions(createdAt);

-- Document comments table
CREATE TABLE IF NOT EXISTS document_comments (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    versionId TEXT,
    content TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT NOT NULL,
    author_avatar TEXT,
    createdAt TEXT NOT NULL,
    selection_from INTEGER,
    selection_to INTEGER,
    resolved INTEGER NOT NULL DEFAULT 0,
    parent_comment_id TEXT,
    FOREIGN KEY (documentId) REFERENCES documents(id),
    FOREIGN KEY (versionId) REFERENCES document_versions(id),
    FOREIGN KEY (parent_comment_id) REFERENCES document_comments(id)
);

-- Create indexes for document_comments table
CREATE INDEX IF NOT EXISTS idx_comments_documentId ON document_comments(documentId);
CREATE INDEX IF NOT EXISTS idx_comments_versionId ON document_comments(versionId);
CREATE INDEX IF NOT EXISTS idx_comments_createdAt ON document_comments(createdAt);
CREATE INDEX IF NOT EXISTS idx_comments_parent ON document_comments(parent_comment_id);

-- Document edits table (for offline edits)
CREATE TABLE IF NOT EXISTS document_edits (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    content TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    FOREIGN KEY (documentId) REFERENCES documents(id)
);

-- Create indexes for document_edits table
CREATE INDEX IF NOT EXISTS idx_edits_documentId ON document_edits(documentId);
CREATE INDEX IF NOT EXISTS idx_edits_timestamp ON document_edits(timestamp);
`;

// Helper functions
const serializeForStorage = (data: any): string => {
  if (typeof data === 'object' && data !== null) {
    return JSON.stringify(data);
  }
  return data || '';
};

const deserializeFromStorage = <T>(data: string | null): T | null => {
  if (!data) return null;
  try {
    return JSON.parse(data) as T;
  } catch (e) {
    return data as unknown as T;
  }
};

// SQLite implementation
let db: Database | null = null;
let isInitialized = false;

/**
 * Initialize SQLite database
 */
export const initSQLite = async (): Promise<void> => {
  if (isInitialized) return;

  try {
    const SQL = await initSqlJs({
      locateFile: (file) => `node_modules/sql.js/dist/${file}`
    });

    db = new SQL.Database();


    execSQL(schemaSQL);

    isInitialized = true;
    console.log('SQLite database initialized successfully');
  } catch (error) {
    console.error('Error initializing SQLite database:', error);
    throw error;
  }
};

/**
 * Close SQLite database
 */
export const closeSQLite = async (): Promise<void> => {
  if (db) {
    db.close();
    db = null;
    isInitialized = false;
    console.log('SQLite database closed');
  }
};

/**
 * Check if SQLite is available in the current environment
 */
export const isSQLiteAvailable = (): boolean => {
  return isInitialized && db !== null;
};

/**
 * Execute raw SQL query
 */
export const executeSQL = (sql: string, params: any[] = []): any => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const stmt = db.prepare(sql);
    stmt.bind(params);
    
    // For queries like SELECT COUNT(*), we need to step()
    const hasResult = stmt.step();
    if (!hasResult) {
      stmt.free();
      return null;
    }
    
    const result = stmt.getAsObject();
    stmt.free();
    
    // Log the result for debugging
    console.log('SQL query result:', { sql, result });
    
    return result;
  } catch (error) {
    console.error('Error executing SQL:', error, sql, params);
    throw error;
  }
};

/**
 * Run SQL query with parameters and return results
 */
export const runSQL = (sql: string, params: any[] = []): void => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const stmt = db.prepare(sql);
    stmt.bind(params);
    stmt.step();
    stmt.free();
  } catch (error) {
    console.error('Error running SQL:', error, sql, params);
    throw error;
  }
};

/**
 * Execute SQL statement for write operations
 */
export const execSQL = (sql: string): any[] => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    return db.exec(sql);
  } catch (error) {
    console.error('Error executing SQL batch:', error, sql);
    throw error;
  }
};

/**
 * Save a document to the database
 */
export const saveDocument = async (document: any): Promise<void> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    // Ensure all required fields have values
    const safeDocument = {
      id: document.id,
      bidId: document.bidId || '',
      title: document.title || `Document ${document.id}`,
      content: document.content || '',
      createdAt: document.createdAt || new Date().toISOString(),
      updatedAt: document.updatedAt || new Date().toISOString(),
      createdBy: document.createdBy || '',
      status: document.status || 'draft',
      lastVersionId: document.lastVersionId || null,
      lastVersion: document.lastVersion || null,
      metadata: document.metadata || {}
    };
    
    // Log the document being saved
    console.log('Saving document to SQLite:', safeDocument);
    
    const metadata = serializeForStorage(safeDocument.metadata || {});
    
    const sql = `
      INSERT OR REPLACE INTO documents (
        id, bidId, title, content, createdAt, updatedAt, 
        createdBy, status, lastVersionId, lastVersion, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    runSQL(sql, [
      safeDocument.id,
      safeDocument.bidId,
      safeDocument.title,
      safeDocument.content,
      safeDocument.createdAt,
      safeDocument.updatedAt,
      safeDocument.createdBy,
      safeDocument.status,
      safeDocument.lastVersionId,
      safeDocument.lastVersion,
      metadata
    ]);
    
    console.log(`Document ${safeDocument.id} saved successfully`);
  } catch (error) {
    console.error('Error saving document:', error);
    throw error;
  }
};

/**
 * Get a document by ID
 */
export const getDocument = async (id: string): Promise<Document | null> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `SELECT * FROM documents WHERE id = ? LIMIT 1`;
    const stmt = db.prepare(sql);
    stmt.bind([id]);
    
    if (!stmt.step()) {
      stmt.free();
      return null;
    }
    
    const result = stmt.getAsObject();
    stmt.free();
    
    if (!result || !result.id) return null;
    
    // Parse metadata, ensure we don't pass null
    const metadata = result.metadata ? 
      deserializeFromStorage<Record<string, any>>(result.metadata as string) || {} : 
      {};
    
    return {
      id: result.id as string,
      bidId: result.bidId as string,
      title: result.title as string,
      content: result.content as string,
      createdAt: result.createdAt as string,
      updatedAt: result.updatedAt as string,
      createdBy: result.createdBy as string,
      status: result.status as any,
      lastVersionId: result.lastVersionId as string,
      lastVersion: result.lastVersion as string,
      metadata
    };
  } catch (error) {
    console.error('Error getting document:', error);
    throw error;
  }
};

/**
 * Get documents by bid ID
 */
export const getDocumentsByBid = async (bidId: string): Promise<Document[]> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `SELECT * FROM documents WHERE bidId = ? ORDER BY updatedAt DESC`;
    const stmt = db.prepare(sql);
    stmt.bind([bidId]);
    
    const documents: Document[] = [];
    while (stmt.step()) {
      const row = stmt.getAsObject();
      
      // Parse metadata, ensure we don't pass null
      const metadata = row.metadata ? 
        deserializeFromStorage<Record<string, any>>(row.metadata as string) || {} : 
        {};
      
      documents.push({
        id: row.id as string,
        bidId: row.bidId as string,
        title: row.title as string,
        content: row.content as string,
        createdAt: row.createdAt as string,
        updatedAt: row.updatedAt as string,
        createdBy: row.createdBy as string,
        status: row.status as any,
        lastVersionId: row.lastVersionId as string,
        lastVersion: row.lastVersion as string,
        metadata
      });
    }
    stmt.free();
    
    return documents;
  } catch (error) {
    console.error('Error getting documents by bid:', error);
    throw error;
  }
};

/**
 * Save a document version
 */
export const saveDocumentVersion = async (version: DocumentVersion): Promise<void> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    // First, check if the parent document exists
    const docExists = await documentExists(version.documentId);
    
    if (!docExists) {
      console.warn(`Parent document ${version.documentId} not found when saving version. Creating placeholder.`);
      // Create a placeholder document
      await saveDocument({
        id: version.documentId,
        bidId: '',
        title: 'Document ' + version.documentId,
        content: version.content || '',
        createdAt: version.metadata.createdAt,
        updatedAt: version.metadata.createdAt,
        createdBy: version.metadata.createdBy.id,
        status: version.metadata.status as any,
        lastVersionId: version.id,
        lastVersion: version.version,
        metadata: {}
      });
    }
    
    const sql = `
      INSERT OR REPLACE INTO document_versions (
        id, documentId, content, version, previousVersionId, diff,
        createdAt, createdBy_id, createdBy_name, createdBy_role, 
        commitMessage, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    runSQL(sql, [
      version.id,
      version.documentId,
      version.content,
      version.version,
      version.previousVersionId || null,
      version.diff || null,
      version.metadata.createdAt,
      version.metadata.createdBy.id,
      version.metadata.createdBy.name,
      version.metadata.createdBy.role,
      version.metadata.commitMessage || null,
      version.metadata.status
    ]);
    
    // Update the document's lastVersionId and lastVersion
    const updateDocSql = `
      UPDATE documents 
      SET lastVersionId = ?, lastVersion = ?, updatedAt = ? 
      WHERE id = ?
    `;
    
    runSQL(updateDocSql, [
      version.id,
      version.version,
      version.metadata.createdAt,
      version.documentId
    ]);
    
    console.log(`Document version ${version.id} saved successfully`);
  } catch (error) {
    console.error('Error saving document version:', error);
    throw error;
  }
};

/**
 * Check if a document exists in the database
 */
async function documentExists(id: string): Promise<boolean> {
  if (!db) return false;
  
  try {
    const sql = `SELECT id FROM documents WHERE id = ? LIMIT 1`;
    const stmt = db.prepare(sql);
    stmt.bind([id]);
    
    const exists = stmt.step();
    stmt.free();
    
    return exists;
  } catch (error) {
    console.error('Error checking if document exists:', error);
    return false;
  }
}

/**
 * Get a document version by ID
 */
export const getDocumentVersion = async (id: string): Promise<DocumentVersion | null> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `SELECT * FROM document_versions WHERE id = ? LIMIT 1`;
    const stmt = db.prepare(sql);
    stmt.bind([id]);
    
    if (!stmt.step()) {
      stmt.free();
      return null;
    }
    
    const result = stmt.getAsObject();
    stmt.free();
    
    if (!result || !result.id) return null;
    
    return {
      id: result.id as string,
      documentId: result.documentId as string,
      content: result.content as string,
      version: result.version as string,
      previousVersionId: result.previousVersionId as string,
      diff: result.diff as string,
      metadata: {
        status: result.status as string,
        createdAt: result.createdAt as string,
        createdBy: {
          id: result.createdBy_id as string,
          name: result.createdBy_name as string,
          role: result.createdBy_role as string
        },
        commitMessage: result.commitMessage as string
      }
    };
  } catch (error) {
    console.error('Error getting document version:', error);
    throw error;
  }
};

/**
 * Get document versions
 */
export const getDocumentVersions = async (documentId: string): Promise<DocumentVersion[]> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `
      SELECT * FROM document_versions 
      WHERE documentId = ? 
      ORDER BY createdAt DESC
    `;
    
    const stmt = db.prepare(sql);
    stmt.bind([documentId]);
    
    const versions: DocumentVersion[] = [];
    while (stmt.step()) {
      const row = stmt.getAsObject();
      versions.push({
        id: row.id as string,
        documentId: row.documentId as string,
        content: row.content as string,
        version: row.version as string,
        previousVersionId: row.previousVersionId as string,
        diff: row.diff as string,
        metadata: {
          status: row.status as string,
          createdAt: row.createdAt as string,
          createdBy: {
            id: row.createdBy_id as string,
            name: row.createdBy_name as string,
            role: row.createdBy_role as string
          },
          commitMessage: row.commitMessage as string
        }
      });
    }
    stmt.free();
    
    return versions;
  } catch (error) {
    console.error('Error getting document versions:', error);
    throw error;
  }
};

/**
 * Get the latest version of a document
 */
export const getLatestVersion = async (documentId: string): Promise<DocumentVersion | null> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `
      SELECT * FROM document_versions 
      WHERE documentId = ? 
      ORDER BY createdAt DESC 
      LIMIT 1
    `;
    
    const stmt = db.prepare(sql);
    stmt.bind([documentId]);
    
    if (!stmt.step()) {
      stmt.free();
      return null;
    }
    
    const result = stmt.getAsObject();
    stmt.free();
    
    if (!result || !result.id) return null;
    
    return {
      id: result.id as string,
      documentId: result.documentId as string,
      content: result.content as string,
      version: result.version as string,
      previousVersionId: result.previousVersionId as string,
      diff: result.diff as string,
      metadata: {
        status: result.status as string,
        createdAt: result.createdAt as string,
        createdBy: {
          id: result.createdBy_id as string,
          name: result.createdBy_name as string,
          role: result.createdBy_role as string
        },
        commitMessage: result.commitMessage as string
      }
    };
  } catch (error) {
    console.error('Error getting latest document version:', error);
    throw error;
  }
};

/**
 * Save a document comment
 */
export const saveDocumentComment = async (comment: DocumentComment & { parent_comment_id?: string }): Promise<void> => {
  if (!db) throw new Error('SQLite database not initialized');

  try {
    const sql = `
      INSERT OR REPLACE INTO document_comments (
        id, documentId, versionId, content, author_id, author_name, author_avatar,
        createdAt, selection_from, selection_to, resolved, parent_comment_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    runSQL(sql, [
      comment.id,
      comment.documentId,
      comment.versionId || null,
      comment.content,
      comment.author.id,
      comment.author.name,
      comment.author.avatar || null,
      comment.createdAt,
      comment.selection?.from || null,
      comment.selection?.to || null,
      comment.resolved ? 1 : 0,
      comment.parent_comment_id || null
    ]);

    // If the comment has replies, save them recursively
    if (comment.replies && comment.replies.length > 0) {
      for (const reply of comment.replies) {
        await saveDocumentComment({
          ...reply,
          parent_comment_id: comment.id,
          documentId: comment.documentId,
        });
      }
    }

    console.log(`Comment ${comment.id} saved successfully`);
  } catch (error) {
    console.error('Error saving document comment:', error);
    throw error;
  }
};

/**
 * Get all comments for a document, handling replies hierarchically
 */
export const getDocumentComments = async (
  documentId: string,
  versionId?: string
): Promise<DocumentComment[]> => {
  if (!db) throw new Error('SQLite database not initialized');

  try {
    let sql = `
      SELECT * FROM document_comments
      WHERE documentId = ? AND parent_comment_id IS NULL
    `;

    const params: any[] = [documentId];

    if (versionId !== undefined && versionId !== null) {
      sql += ` AND versionId = ?`;
      params.push(versionId);
    }

    sql += ` ORDER BY createdAt ASC`;

    const stmt = db.prepare(sql);
    stmt.bind(params);

    const comments: DocumentComment[] = [];
    while (stmt.step()) {
      const row = stmt.getAsObject();
      comments.push({
        id: row.id as string,
        documentId: row.documentId as string,
        versionId: row.versionId as string | undefined,
        content: row.content as string,
        author: {
          id: row.author_id as string,
          name: row.author_name as string,
          avatar: row.author_avatar as string
        },
        createdAt: row.createdAt as string,
        selection: row.selection_from !== null ? {
          from: row.selection_from as number,
          to: row.selection_to as number
        } : undefined,
        resolved: Boolean(row.resolved),
        replies: []
      });
    }
    stmt.free();

    return comments;
  } catch (error) {
    console.error('Error getting document comments:', error);
    throw error;
  }
};

/**
 * Get the hierarchical structure of comments (with replies)
 */
export const getDocumentCommentsWithReplies = async (
  documentId: string,
  versionId?: string
): Promise<DocumentComment[]> => {
  if (!db) throw new Error('SQLite database not initialized');

  try {
    // First get all parent comments
    const parentComments = await getDocumentComments(documentId, versionId);

    // For each parent comment, get its replies
    for (const comment of parentComments) {
      let replySql = `
        SELECT * FROM document_comments
        WHERE parent_comment_id = ?
      `;
      const params: any[] = [comment.id];

      if (versionId !== undefined && versionId !== null) {
        replySql += ` AND versionId = ?`;
        params.push(versionId);
      }

      replySql += ` ORDER BY createdAt ASC`;

      const stmt = db.prepare(replySql);
      stmt.bind(params);

      const replies: DocumentComment[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        replies.push({
          id: row.id as string,
          documentId: row.documentId as string,
          versionId: row.versionId as string,
          content: row.content as string,
          author: {
            id: row.author_id as string,
            name: row.author_name as string,
            avatar: row.author_avatar as string
          },
          createdAt: row.createdAt as string,
          selection: row.selection_from !== null ? {
            from: row.selection_from as number,
            to: row.selection_to as number
          } : undefined,
          resolved: Boolean(row.resolved),
          replies: []
        });
      }
      stmt.free();

      comment.replies = replies;
    }

    return parentComments;
  } catch (error) {
    console.error('Error getting document comments with replies:', error);
    throw error;
  }
};

/**
 * Save a document edit (for offline editing)
 */
export const saveDocumentEdit = async (edit: DocumentEdit): Promise<void> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `
      INSERT OR REPLACE INTO document_edits (
        id, documentId, content, timestamp
      ) VALUES (?, ?, ?, ?)
    `;
    
    runSQL(sql, [
      edit.id,
      edit.documentId,
      edit.content,
      edit.timestamp
    ]);
    
    console.log(`Document edit ${edit.id} saved successfully`);
  } catch (error) {
    console.error('Error saving document edit:', error);
    throw error;
  }
};

export const getDocumentEdits = async (documentId: string): Promise<DocumentEdit[]> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `
      SELECT * FROM document_edits 
      WHERE documentId = ? 
      ORDER BY timestamp ASC
    `;
    
    const stmt = db.prepare(sql);
    stmt.bind([documentId]);
    
    const edits: DocumentEdit[] = [];
    while (stmt.step()) {
      const row = stmt.getAsObject();
      edits.push({
        id: row.id as string,
        documentId: row.documentId as string,
        content: row.content as string,
        timestamp: row.timestamp as string
      });
    }
    stmt.free();
    
    return edits;
  } catch (error) {
    console.error('Error getting document edits:', error);
    throw error;
  }
};

export const clearDocumentEdits = async (documentId: string): Promise<void> => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    const sql = `DELETE FROM document_edits WHERE documentId = ?`;
    
    runSQL(sql, [documentId]);
    
    console.log(`Document edits for ${documentId} cleared successfully`);
  } catch (error) {
    console.error('Error clearing document edits:', error);
    throw error;
  }
};

// Sync operations
export const setupOfflineSync = (): void => {
  // Implement offline sync logic
  console.log('Setting up offline sync for SQLite');
};

export const exportDatabase = (): Uint8Array => {
  if (!db) throw new Error('SQLite database not initialized');
  
  try {
    return db.export();
  } catch (error) {
    console.error('Error exporting database:', error);
    throw error;
  }
};

export const importDatabase = (data: Uint8Array): void => {
  try {
    closeSQLite();
    initSqlJs({
      locateFile: (file) => `https://sql.js.org/dist/${file}`
    }).then(SQL => {
      db = new SQL.Database(data);
      isInitialized = true;
      console.log('SQLite database imported successfully');
    });
  } catch (error) {
    console.error('Error importing database:', error);
    throw error;
  }
};

/**
 * Check if the SQLite database has any existing data
 */
export const hasSQLiteData = async (): Promise<boolean> => {
  if (!db) {
    try {
      await initSQLite();
    } catch (error) {
      console.error('Error initializing SQLite database:', error);
      return false;
    }
  }
  
  if (!isSQLiteAvailable()) {
    return false;
  }
  
  try {
    // Check if any documents exist
    const documentCount = executeSQL('SELECT COUNT(*) as count FROM documents', []);
    if (documentCount && documentCount.count > 0) {
      return true;
    }
    
    // Check if any versions exist
    const versionCount = executeSQL('SELECT COUNT(*) as count FROM document_versions', []);
    if (versionCount && versionCount.count > 0) {
      return true;
    }
    
    // Check if any comments exist
    const commentCount = executeSQL('SELECT COUNT(*) as count FROM document_comments', []);
    if (commentCount && commentCount.count > 0) {
      return true;
    }
    
    // Check if any edits exist
    const editCount = executeSQL('SELECT COUNT(*) as count FROM document_edits', []);
    if (editCount && editCount.count > 0) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking SQLite data:', error);
    return false;
  }
};

const sqlitedb: SQLiteDatabase = {
  initSQLite,
  closeSQLite,
  isSQLiteAvailable,
  executeSQL,
  runSQL,
  execSQL,
  saveDocument,
  getDocument,
  getDocumentsByBid,
  saveDocumentVersion,
  getDocumentVersion,
  getDocumentVersions,
  getLatestVersion,
  saveDocumentComment,
  getDocumentComments,
  getDocumentCommentsWithReplies,
  saveDocumentEdit,
  getDocumentEdits,
  clearDocumentEdits,
  setupOfflineSync,
  exportDatabase,
  importDatabase,
  hasSQLiteData,
};

export default sqlitedb;