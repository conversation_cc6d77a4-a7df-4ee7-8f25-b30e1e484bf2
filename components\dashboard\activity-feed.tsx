'use client';

import { formatDistanceToNow } from 'date-fns';
import { 
  FileText, 
  PlusCircle, 
  RefreshCw, 
  ArrowRightLeft,
  Clock
} from 'lucide-react';
import Link from 'next/link';
import { withErrorBoundary } from '@/components/error-boundary';

interface ActivityItem {
  id: string;
  type: 'bid_created' | 'bid_updated' | 'status_changed';
  title: string;
  timestamp: string;
  details?: string;
}

interface ActivityFeedProps {
  activities: ActivityItem[];
}

function ActivityFeedComponent({ activities }: ActivityFeedProps) {
  if (!activities || activities.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
        <Clock className="h-12 w-12 mb-2 opacity-20" />
        <p>No recent activity</p>
      </div>
    );
  }

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'bid_created':
        return <PlusCircle className="h-5 w-5 text-green-500" />;
      case 'bid_updated':
        return <RefreshCw className="h-5 w-5 text-blue-500" />;
      case 'status_changed':
        return <ArrowRightLeft className="h-5 w-5 text-amber-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getActivityTitle = (activity: ActivityItem) => {
    switch (activity.type) {
      case 'bid_created':
        return `New bid created: ${activity.title}`;
      case 'bid_updated':
        return `Bid updated: ${activity.title}`;
      case 'status_changed':
        return `Status changed: ${activity.title}`;
      default:
        return activity.title;
    }
  };

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start gap-3 pb-4 border-b last:border-0">
          <div className="mt-0.5">{getActivityIcon(activity.type)}</div>
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <Link href={`/bids/${activity.id}`} className="font-medium hover:underline">
                {getActivityTitle(activity)}
              </Link>
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
              </span>
            </div>
            {activity.details && (
              <p className="text-sm text-muted-foreground">{activity.details}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

// Export the component wrapped with an error boundary
export const ActivityFeed = withErrorBoundary(ActivityFeedComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in ActivityFeed component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});
