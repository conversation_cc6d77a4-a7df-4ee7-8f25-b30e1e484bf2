"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { <PERSON>, Settings, User, LogOut } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { ThemeToggle } from "@/components/theme-toggle";
import { withErrorBoundary } from "@/components/error-boundary";

function HeaderComponent() {
  const { user, logout, isLoading } = useAuth();
  const { toast } = useToast();

  // Debug: Log user state on component mount and when it changes
  useEffect(() => {
    console.log("Header component - Auth state:", { user, isLoading });
  }, [user, isLoading]);

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  return (
    <header className="border-b border-border/40 bg-background/95 backdrop-blur-sm sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              BidManager
            </Link>
            <NavigationMenu>
              <NavigationMenuList className="space-x-1">
                <NavigationMenuItem>
                  <Link href="/" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "rounded-md transition-all duration-200")}>
                      Dashboard
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/bids" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "rounded-md transition-all duration-200")}>
                      Bids
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/tasks" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "rounded-md transition-all duration-200")}>
                      Tasks
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/analytics" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "rounded-md transition-all duration-200")}>
                      Analytics
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <Link href="/team" legacyBehavior passHref>
                    <NavigationMenuLink className={cn(navigationMenuTriggerStyle(), "rounded-md transition-all duration-200")}>
                      Team
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <Button variant="ghost" size="icon" aria-label="Notifications" className="rounded-full hover:bg-accent/10 hover:text-accent transition-all duration-200">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" aria-label="Settings" className="rounded-full hover:bg-accent/10 hover:text-accent transition-all duration-200">
              <Settings className="h-5 w-5" />
            </Button>

            {isLoading ? (
              <Button variant="ghost" size="icon" aria-label="Loading user data" disabled className="rounded-full">
                <User className="h-5 w-5 animate-pulse" />
              </Button>
            ) : user ? (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium hidden md:inline-block">
                  {user.userId}
                </span>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" aria-label="User menu" className="relative rounded-full hover:bg-accent/10 hover:text-accent transition-all duration-200">
                      <User className="h-5 w-5" />
                      <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground shadow-md">
                        <span className="sr-only">User logged in</span>
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56 rounded-xl border-border/40 shadow-lg">
                    <DropdownMenuLabel className="font-semibold">User Profile</DropdownMenuLabel>
                    <DropdownMenuItem className="flex items-center gap-2 rounded-md focus:bg-accent/10 focus:text-accent">
                      <User className="h-4 w-4" />
                      <span>ID: {user.userId}</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-border/40" />
                    <DropdownMenuItem
                      onClick={handleLogout}
                      className="text-destructive flex items-center gap-2 cursor-pointer rounded-md focus:bg-destructive/10"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Logout</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <Button variant="default" size="sm" asChild className="rounded-full shadow-md hover:shadow-lg transition-all duration-200">
                <Link href="/login">Login</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

// Export the component wrapped with an error boundary
export const Header = withErrorBoundary(HeaderComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in Header component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full'
});