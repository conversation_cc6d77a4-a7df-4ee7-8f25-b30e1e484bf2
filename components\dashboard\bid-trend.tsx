'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { withErrorBoundary } from '@/components/error-boundary';

interface BidTrendProps {
  data: Array<{ month: string; count: number }>;
}

function BidTrendComponent({ data }: BidTrendProps) {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Bid Trend</CardTitle>
          <CardDescription>
            Number of bids created in the last 6 months
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bid Trend</CardTitle>
        <CardDescription>
          Number of bids created in the last 6 months
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip 
                formatter={(value) => [`${value} bids`, 'Count']}
                labelFormatter={(label) => `Month: ${label}`}
              />
              <Bar dataKey="count" fill="#3b82f6" name="Bids Created" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

// Export the component wrapped with an error boundary
export const BidTrend = withErrorBoundary(BidTrendComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in BidTrend component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
