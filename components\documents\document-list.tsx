"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  FileText, 
  FilePlus, 
  Calendar, 
  Clock, 
  User,
  Loader2, 
  Archive, 
  FileEdit,
  Trash2, 
  Wifi, 
  WifiOff 
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useIndexedDB } from "@/hooks/useIndexedDB";
import { formatDistanceToNow } from "date-fns";
import { getDocumentsByBid as getDocumentsByBidId, getDocument, saveDocument, getDocumentVersions } from "@/lib/indexeddb";
import { withErrorBoundary } from "@/components/error-boundary";

interface DocumentListProps {
  bidId: string;
  onCreate?: () => void;
}

function DocumentListComponent({ bidId, onCreate }: DocumentListProps) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { isDBAvailable, isOnline } = useIndexedDB();

  const loadDocuments = useCallback(async () => {
    setIsLoading(true);
    try {
      // Get all documents from IndexedDB
      let documents: any[] = [];
      
      if (bidId) {
        documents = await getDocumentsByBidId(bidId);
      } else {
        // If no bidId is provided, return empty array for now
        // In a complete implementation, we might fetch all documents
        documents = [];
      }
      
      setDocuments(documents);
      setError(null);
    } catch (err: any) {
      console.error("Error loading documents:", err);
      setError(err.message || "An error occurred while loading documents");
      toast({
        title: "Error",
        description: err.message || "Failed to load documents",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [bidId, toast]);

  useEffect(() => {
    loadDocuments();
  }, [loadDocuments]);

  const handleDeleteDocument = async (documentId: string) => {
    if (!confirm("Are you sure you want to delete this document?")) {
      return;
    }
    
    try {
      const openRequest = indexedDB.open("BidManagementDB", 1);
      
      openRequest.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction("documents", "readwrite");
        const store = transaction.objectStore("documents");
        const request = store.delete(documentId);
        
        request.onsuccess = () => {
          setDocuments(documents.filter(doc => doc.id !== documentId));
          toast({
            title: "Document deleted",
            description: "The document has been deleted",
          });
        };
        
        request.onerror = () => {
          toast({
            title: "Error",
            description: "Failed to delete document",
            variant: "destructive",
          });
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Could not delete document",
        variant: "destructive",
      });
    }
  };

  const handleCreateDocument = () => {
    if (onCreate) {
      onCreate();
    }
  };

  const filteredDocuments = () => {
    switch (activeTab) {
      case "recent":
        // Documents updated in the last 7 days
        return documents.filter(doc => {
          const updatedAt = new Date(doc.updatedAt);
          const sevenDaysAgo = new Date();
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
          return updatedAt >= sevenDaysAgo;
        });
      case "drafts":
        // Documents with draft status
        return documents.filter(doc => doc.status === "draft" || !doc.status);
      case "archived":
        // Archived documents
        return documents.filter(doc => doc.status === "archived");
      case "all":
      default:
        return documents;
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Documents</CardTitle>
            <CardDescription>
              Manage your bid documents
            </CardDescription>
          </div>
          <div className="flex gap-2 items-center">
            {/* Network status indicator */}
            {isDBAvailable && (
              <div className="text-xs text-muted-foreground">
                {isOnline ? (
                  <div className="flex items-center text-green-500">
                    <Wifi className="h-3 w-3 mr-1" />
                    <span>Online</span>
                  </div>
                ) : (
                  <div className="flex items-center text-amber-500">
                    <WifiOff className="h-3 w-3 mr-1" />
                    <span>Offline</span>
                  </div>
                )}
              </div>
            )}
            <Button size="sm" onClick={handleCreateDocument}>
              <FilePlus className="h-4 w-4 mr-2" />
              New Document
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        <TabsList className="mx-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="recent">Recent</TabsTrigger>
          <TabsTrigger value="drafts">Drafts</TabsTrigger>
          <TabsTrigger value="archived">Archived</TabsTrigger>
        </TabsList>
        
        <CardContent className="flex-1 p-0 pt-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : filteredDocuments().length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <FileText className="h-12 w-12 mb-4" />
              <h3 className="text-lg font-medium mb-2">No documents found</h3>
              <p className="text-sm text-center mb-4">
                {activeTab === "all" 
                  ? "Create your first document to get started" 
                  : "There are no documents in this category"}
              </p>
              <Button onClick={handleCreateDocument}>
                <FilePlus className="h-4 w-4 mr-2" />
                Create Document
              </Button>
            </div>
          ) : (
            <ScrollArea className="h-[calc(100vh-320px)] px-6">
              <div className="space-y-4">
                {filteredDocuments().map((doc) => (
                  <div key={doc.id} className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-5 w-5 text-primary" />
                          <Link href={`/bids/${bidId}/documents/${doc.id}`} className="font-medium text-primary hover:underline">
                            {doc.title || "Untitled Document"}
                          </Link>
                          {doc.status && (
                            <Badge variant={doc.status === 'draft' ? 'outline' : doc.status === 'archived' ? 'secondary' : 'default'}>
                              {doc.status}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {doc.description || "No description"}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/bids/${bidId}/documents/${doc.id}`}>
                            <FileEdit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDeleteDocument(doc.id)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </div>
                    
                    <Separator className="my-3" />
                    
                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {doc.createdBy?.name || "Unknown"}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(doc.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Updated {formatDistanceToNow(new Date(doc.updatedAt), { addSuffix: true })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Tabs>
    </Card>
  );
}

// Export the component wrapped with an error boundary
export const DocumentList = withErrorBoundary(DocumentListComponent, {
  onError: (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Error in DocumentList component:', error);
    console.error('Component stack:', errorInfo.componentStack);
  },
  className: 'w-full h-full'
});
