export type Status = 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CLOSED';

export interface Bid {
  bidId: string;
  title: string;
  description: string;
  budget: number;
  status: Status;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  assignedTo: string[];
  documents: Document[];
  comments: Comment[];
  // Appworks-specific fields
  opportunitySource?: string;
  businessNeed?: string;
  strategicAlignment?: string;
  potentialBenefits?: string;
  preliminaryScope?: string;
  stakeholders?: string;
  riskAssessment?: string;
  bidStrategy?: string;
  competitiveAnalysis?: string;
  winStrategy?: string;
  technicalApproach?: string;
  pricingStrategy?: string;
  qualityStandards?: string;
  complianceRequirements?: string;
}

export interface BidFilterParams {
  title?: string;
  status?: Status | 'all';
  opportunitySource?: string;
  strategicAlignment?: string;
  budgetMin?: number;
  budgetMax?: number;
  createdDateFrom?: string;
  createdDateTo?: string;
}

export type FilterOperator = 'eq' | 'ne' | 'lt' | 'gt' | 'ge' | 'le' | 'Like' | 'IsNull' | 'IsNotNull' | 'InList' | 'NotInList' | 'Between';

export interface AppworksFilterParameter {
  name: string;
  comparison: {
    value?: string | number;
    values?: (string | number)[];
    from?: string | number;
    to?: string | number;
    operator: FilterOperator;
  };
}

export interface Document {
  id: string;
  name: string;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
}

export interface Comment {
  id: string;
  content: string;
  createdBy: string;
  createdAt: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'MANAGER' | 'BIDDER';
  avatar: string;
}

export interface VendorResponse {
  id: string;
  vendorId: string;
  bidId: string;
  submissionDate: string;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'SHORTLISTED' | 'REJECTED' | 'AWARDED';
  technicalScore?: number;
  commercialScore?: number;
  presentationScore?: number;
  totalScore?: number;
  evaluationNotes: EvaluationNote[];
  documents: Document[];
}

export interface EvaluationNote {
  id: string;
  category: 'TECHNICAL' | 'COMMERCIAL' | 'PRESENTATION' | 'DUE_DILIGENCE';
  content: string;
  score?: number;
  createdBy: string;
  createdAt: string;
}

export interface EvaluationCriteria {
  id: string;
  category: string;
  description: string;
  weight: number;
  minimumScore?: number;
}

// Document types for rich text editing
export type DocumentStatus = 'draft' | 'under_review' | 'approved';

export interface DocumentVersion {
  id: string;
  documentId: string;
  content: string;
  version: string;
  previousVersionId: string | null;
  diff?: string;
  metadata: {
    status: string;
    createdAt: string;
    createdBy: {
      id: string;
      name: string;
      role: string;
    };
    commitMessage?: string;
  };
}

export interface DiffBlock {
  startPosition: number;
  endPosition: number;
  content: string;
  type?: 'addition' | 'deletion' | 'modification';
}

export interface DocumentComment {
  id: string;
  documentId: string;
  versionId?: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  content: string;
  createdAt: string;
  selection?: {
    from: number;
    to: number;
  };
  resolved: boolean;
  replies: DocumentComment[];
  parent_comment_id?: string;
}

export interface DocumentEditorProps {
  documentId: string;
  initialContent?: string;
  readOnly?: boolean;
  bidId?: string;
  onSave?: (content: string) => void;
  onVersionCreate?: (version: DocumentVersion) => void;
  versions?: DocumentVersion[];
  onCompareVersions?: (sourceVersionId: string, targetVersionId: string) => Promise<any>;
  onRestoreVersion?: (versionId: string) => Promise<void>;
  isLoadingVersions?: boolean;
}
