/**
 * Simplified test for SQLite database implementation
 */

import * as SQLiteDB from '../lib/sqlitedb';
import { initSQLite, closeSQLite } from '../lib/sqlitedb';
import { Document } from '../types/sqlitedb';
import {
  resetMockDatabase,
  addMockDocument,
} from './helpers/sqliteTestHelpers';

describe('SQLite Database - Simple Tests', () => {
  // Initialize database before tests
  beforeAll(async () => {
    await initSQLite();
  });



  // Close database after tests
  afterAll(async () => {
    await closeSQLite();
  });

  describe('Basic Document Operations', () => {
    test('should save and retrieve a document', async () => {
      console.log('Starting test: should save and retrieve a document');
      
      // Create test document
      const testDoc: Document = {
        id: 'test-doc-1',
        bidId: 'test-bid-1',
        title: 'Test Document',
        content: 'Test content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      // Pre-populate mock database
      console.log('Adding mock document');
      addMockDocument(testDoc);
      
      console.log('Saving document to SQLite');
      await SQLiteDB.saveDocument(testDoc);

      console.log('Retrieving document from SQLite');
      const retrievedDoc = await SQLiteDB.getDocument(testDoc.id);
      
      console.log('Retrieved document:', retrievedDoc);
      expect(retrievedDoc).not.toBeNull();
      expect(retrievedDoc?.id).toBe(testDoc.id);
      expect(retrievedDoc?.title).toBe(testDoc.title);
      
      console.log('Test completed successfully');
    });
  });
});
