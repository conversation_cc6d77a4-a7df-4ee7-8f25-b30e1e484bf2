/**
 * SQLite Test Helpers
 * 
 * Provides utility functions to populate the mock database for testing
 */

import { Document } from '../../types/sqlitedb';
import { DocumentVersion, DocumentComment, DocumentStatus } from '../../types';

// Define DocumentEdit type for testing
export interface DocumentEdit {
  id: string;
  documentId: string;
  content: string;
  timestamp: string;
  userId: string;
  userName?: string;
  status?: string;
  metadata?: Record<string, any>;
}

// Define extended types for SQLite storage that include SQLite-specific fields
export interface SQLiteDocumentVersion extends DocumentVersion {
  createdBy_id?: string;
  createdBy_name?: string;
  createdBy_role?: string;
  commitMessage?: string;
}

export interface SQLiteDocumentComment extends DocumentComment {
  parent_comment_id?: string;
}

// Access the global mock database storage
declare global {
  var __dbStorage: {
    documents: Record<string, Document>;
    documentVersions: Record<string, SQLiteDocumentVersion>;
    documentComments: Record<string, SQLiteDocumentComment>;
    documentEdits: Record<string, DocumentEdit>;
  };
}

/**
 * Reset all mock database collections
 */
export function resetMockDatabase() {
  global.__dbStorage.documents = {};
  global.__dbStorage.documentVersions = {};
  global.__dbStorage.documentComments = {};
  global.__dbStorage.documentEdits = {};
}

/**
 * Add a document to the mock database
 */
export function addMockDocument(document: Partial<Document>): Document {
  // Create a new document with all required fields
  const newDoc: Document = {
    id: document.id || `doc-${Date.now()}`,
    bidId: document.bidId || '',
    title: document.title || 'Test Document',
    content: document.content || '',
    createdBy: document.createdBy || 'test-user',
    createdAt: document.createdAt || new Date().toISOString(),
    updatedAt: document.updatedAt || new Date().toISOString(),
    status: document.status || 'draft',
    lastVersionId: document.lastVersionId || undefined,
    lastVersion: document.lastVersion || undefined,
    metadata: document.metadata || {}
  };
  
  // Handle any additional fields from the input document
  Object.entries(document).forEach(([key, value]) => {
    if (value !== undefined) {
      (newDoc as any)[key] = value;
    }
  });
  
  global.__dbStorage.documents[newDoc.id] = newDoc;
  return newDoc;
}

/**
 * Add multiple documents to the mock database
 */
export function addMockDocuments(documents: Partial<Document>[]) {
  documents.forEach(doc => addMockDocument(doc));
}

/**
 * Add a document version to the mock database
 */
export function addMockDocumentVersion(version: Partial<DocumentVersion> & { documentId: string }): SQLiteDocumentVersion {
  const newVersion: SQLiteDocumentVersion = {
    id: version.id || `version-${Date.now()}`,
    documentId: version.documentId,
    version: version.version || '1.0.0',
    content: version.content || '',
    previousVersionId: version.previousVersionId || null,
    diff: version.diff || '',
    metadata: {
      status: version.metadata?.status || 'draft' as DocumentStatus,
      createdAt: version.metadata?.createdAt || new Date().toISOString(),
      createdBy: version.metadata?.createdBy || {
        id: 'test-user-id',
        name: 'Test User',
        role: 'user'
      },
      commitMessage: version.metadata?.commitMessage || 'Test version'
    }
  };
  
  // Add SQLite-specific fields for the mock
  newVersion.createdBy_id = version.metadata?.createdBy?.id || 'test-user-id';
  newVersion.createdBy_name = version.metadata?.createdBy?.name || 'Test User';
  newVersion.createdBy_role = version.metadata?.createdBy?.role || 'user';
  newVersion.commitMessage = version.metadata?.commitMessage || 'Test version';
  
  global.__dbStorage.documentVersions[newVersion.id] = newVersion;
  
  // Update the document's lastVersionId and lastVersion
  const doc = global.__dbStorage.documents[version.documentId];
  if (doc) {
    doc.lastVersionId = newVersion.id;
    doc.lastVersion = newVersion.version;
    doc.updatedAt = new Date().toISOString();
  }
  
  return newVersion;
}

/**
 * Add multiple document versions to the mock database
 */
export function addMockDocumentVersions(versions: DocumentVersion[]) {
  versions.forEach(version => addMockDocumentVersion(version));
}

/**
 * Add a document comment to the mock database
 */
export function addMockDocumentComment(comment: Partial<DocumentComment> & { 
  documentId: string; 
  content: string; 
  author: { id: string; name: string; avatar?: string; } 
}): SQLiteDocumentComment {
  const newComment: SQLiteDocumentComment = {
    id: comment.id || `comment-${Date.now()}`,
    documentId: comment.documentId,
    versionId: comment.versionId,
    content: comment.content,
    author: {
      id: comment.author.id,
      name: comment.author.name,
      avatar: comment.author.avatar
    },
    createdAt: comment.createdAt || new Date().toISOString(),
    selection: comment.selection,
    resolved: comment.resolved || false,
    replies: comment.replies || []
  };
  
  // Add SQLite-specific fields
  newComment.parent_comment_id = (comment as any).parent_comment_id || null;
  
  global.__dbStorage.documentComments[newComment.id] = newComment;
  return newComment;
}

/**
 * Add multiple document comments to the mock database
 */
export function addMockDocumentComments(comments: DocumentComment[]) {
  comments.forEach(comment => addMockDocumentComment(comment));
}

/**
 * Add a document edit to the mock database
 */
export function addMockDocumentEdit(edit: Omit<DocumentEdit, 'id' | 'timestamp'> & { id?: string, timestamp?: string }): DocumentEdit {
  const newEdit: DocumentEdit = {
    id: edit.id || `edit-${Date.now()}`,
    documentId: edit.documentId,
    content: edit.content,
    timestamp: edit.timestamp || new Date().toISOString(),
    userId: edit.userId || 'test-user-id',
    userName: edit.userName,
    status: edit.status,
    metadata: edit.metadata
  };
  
  global.__dbStorage.documentEdits[newEdit.id] = JSON.parse(JSON.stringify(newEdit));
  return newEdit;
}

/**
 * Add multiple document edits to the mock database
 */
export function addMockDocumentEdits(edits: DocumentEdit[]) {
  edits.forEach(edit => addMockDocumentEdit(edit));
}

/**
 * Get document by ID
 */
export function getMockDocument(id: string): Document | null {
  // Ensure we return a deep copy to avoid reference issues
  const doc = global.__dbStorage.documents[id];
  return doc ? JSON.parse(JSON.stringify(doc)) : null;
}

/**
 * Get documents by bid ID
 */
export function getMockDocumentsByBidId(bidId: string): Document[] {
  return Object.values(global.__dbStorage.documents).filter(doc => doc.bidId === bidId);
}

/**
 * Get document versions by document ID
 */
export function getMockDocumentVersionsByDocumentId(documentId: string): SQLiteDocumentVersion[] {
  return Object.values(global.__dbStorage.documentVersions)
    .filter(version => version.documentId === documentId)
    .map(version => JSON.parse(JSON.stringify(version)));
}

/**
 * Get document comments by document ID
 */
export function getMockDocumentCommentsByDocumentId(documentId: string): SQLiteDocumentComment[] {
  return Object.values(global.__dbStorage.documentComments)
    .filter(comment => comment.documentId === documentId)
    .map(comment => JSON.parse(JSON.stringify(comment)));
}

/**
 * Get document edits by document ID
 */
export function getMockDocumentEditsByDocumentId(documentId: string): DocumentEdit[] {
  return Object.values(global.__dbStorage.documentEdits)
    .filter(edit => edit.documentId === documentId)
    .map(edit => JSON.parse(JSON.stringify(edit)));
}

/**
 * Clear document edits by document ID
 */
export function clearMockDocumentEditsByDocumentId(documentId: string) {
  Object.keys(global.__dbStorage.documentEdits).forEach(key => {
    if (global.__dbStorage.documentEdits[key].documentId === documentId) {
      delete global.__dbStorage.documentEdits[key];
    }
  });
}
